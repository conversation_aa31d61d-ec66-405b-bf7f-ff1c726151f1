import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload, REALTIME_SUBSCRIBE_STATES } from '@supabase/supabase-js';
import { ExtendedRealtimeChannel, asExtendedChannel } from '@/types/supabase';

// Types for payload data
export interface SongRequestPayload {
  id: string;
  session_id: string;
  song_title: string;
  artist_name: string;
  requester_name: string;
  status: 'pending' | 'approved' | 'auto-approved' | 'declined' | 'played' | 'archived';
  created_at?: string;
  updated_at?: string;
  requester_ip?: string;
  apple_music_id?: string;
  album_artwork?: string;
}

export interface SessionPayload {
  id: string;
  dj_id: string;
  name: string;
  active: boolean;
  created_at?: string;
  updated_at?: string;
}

export type RealtimeCallback<T> = (payload: RealtimePostgresChangesPayload<T>) => void;

/**
 * Helper to safely parse new/old data from real-time payloads
 */
export const getPayloadData = <T>(payload: RealtimePostgresChangesPayload<T>): T => {
  return (payload.new || payload.old) as T;
};

/**
 * Connection management for all realtime channels
 */
class RealtimeConnectionManager {
  private static instance: RealtimeConnectionManager;
  private retryCounters: Map<string, number> = new Map();
  private readonly MAX_RETRY_DELAY = 30000; // 30 seconds
  private readonly MAX_RETRIES = 5; // Maximum number of retry attempts
  private channelMap: Map<string, RealtimeChannel> = new Map();
  private retryTimers: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {}

  public static getInstance(): RealtimeConnectionManager {
    if (!RealtimeConnectionManager.instance) {
      RealtimeConnectionManager.instance = new RealtimeConnectionManager();
    }
    return RealtimeConnectionManager.instance;
  }

  public getChannel(channelId: string): RealtimeChannel | undefined {
    return this.channelMap.get(channelId);
  }

  public registerChannel(channelId: string, channel: RealtimeChannel): void {
    // Clean up any existing channel with the same ID
    if (this.channelMap.has(channelId)) {
      this.cleanupChannel(channelId);
    }

    this.channelMap.set(channelId, channel);
  }

  public cleanupChannel(channelId: string): void {
    const channel = this.channelMap.get(channelId);
    if (channel) {
      try {
        supabase.removeChannel(channel);
      } catch (err) {
        console.error(`Error removing channel ${channelId}:`, err);
      }
      this.channelMap.delete(channelId);
    }

    // Clear any retry timers
    const timer = this.retryTimers.get(channelId);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(channelId);
    }

    // Reset retry counter
    this.retryCounters.delete(channelId);
  }

  public cleanupAllChannels(): void {
    for (const channelId of this.channelMap.keys()) {
      this.cleanupChannel(channelId);
    }
  }

  public handleSubscriptionStatus(channel: RealtimeChannel, status: string, description: string): void {
    const channelTopic = channel.topic;
    const channelId = channelTopic.split(':')[0]; // Extract base channel ID

    console.log(`📡 ${description} subscription status: ${status}`);

    // Handle different subscription states
    switch (status) {
      case REALTIME_SUBSCRIBE_STATES.SUBSCRIBED:
        console.log(`✅ ${description} successfully subscribed`);
        // Reset retry counter on successful subscription
        this.retryCounters.set(channelId, 0);
        break;

      case REALTIME_SUBSCRIBE_STATES.TIMED_OUT:
        console.warn(`⏰ ${description} subscription timed out`);
        this.handleConnectionError(channelId, description);
        break;

      case REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR:
        console.error(`❌ ${description} subscription error`);
        this.handleConnectionError(channelId, description);
        break;

      case REALTIME_SUBSCRIBE_STATES.CLOSED:
        console.log(`📡 ${description} subscription closed`);
        break;

      default:
        console.log(`📡 ${description} subscription status: ${status}`);
        break;
    }
  }

  private handleConnectionError(channelId: string, description: string): void {
    // Get current retry count or initialize to 0
    let retryCount = this.retryCounters.get(channelId) || 0;

    // Check if we've exceeded max retries
    if (retryCount >= this.MAX_RETRIES) {
      console.error(`❌ Max retry attempts (${this.MAX_RETRIES}) reached for ${description}`);
      return;
    }

    retryCount++;
    this.retryCounters.set(channelId, retryCount);

    // Calculate exponential backoff with jitter
    const baseDelay = Math.min(1000 * Math.pow(2, retryCount), this.MAX_RETRY_DELAY);
    const jitter = Math.random() * 0.3 * baseDelay; // Add 0-30% jitter
    const retryDelay = Math.floor(baseDelay + jitter);

    console.log(`🔄 Will attempt to reconnect ${description} in ${retryDelay}ms (attempt #${retryCount}/${this.MAX_RETRIES})`);

    // Clear any existing retry timer for this channel
    const existingTimer = this.retryTimers.get(channelId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new retry timer
    const timer = setTimeout(() => {
      console.log(`🔄 Attempting to reconnect ${description}...`);

      // Don't reuse the same channel - create a new one instead
      this.cleanupChannel(channelId);

      // Let the caller handle the reconnection by returning a special status
      if (this.reconnectCallback) {
        this.reconnectCallback(channelId);
      }

    }, retryDelay);

    this.retryTimers.set(channelId, timer);
  }

  // Callback for reconnection handling
  private reconnectCallback?: (channelId: string) => void;

  public setReconnectCallback(callback: (channelId: string) => void): void {
    this.reconnectCallback = callback;
  }
}

// Create a singleton instance for the entire app
const connectionManager = RealtimeConnectionManager.getInstance();

// Helper function to create a unique channel ID with timestamp to avoid conflicts
const createUniqueChannelId = (baseId: string): string => {
  return `${baseId}_${Date.now()}`;
};

/**
 * Subscribe to song request updates for a specific session with better error handling
 */
export const subscribeToSessionRequests = (
  sessionId: string,
  onInsert?: RealtimeCallback<SongRequestPayload>,
  onUpdate?: RealtimeCallback<SongRequestPayload>,
  onDelete?: RealtimeCallback<SongRequestPayload>
): RealtimeChannel => {
  console.log(`Subscribing to session requests for session ${sessionId}`);

  const channelName = createUniqueChannelId(`session-requests-${sessionId}`);

  try {
    // Clean up any existing channel with the same base ID
    const existingChannels = Array.from(supabase.getChannels()).filter(
      ch => ch.topic.startsWith(`session-requests-${sessionId}`)
    );

    for (const channel of existingChannels) {
      try {
        supabase.removeChannel(channel);
      } catch (err) {
        console.error("Error removing existing channel:", err);
      }
    }

    const channel = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        console.log('New song request:', payload);
        onInsert && onInsert(payload as RealtimePostgresChangesPayload<SongRequestPayload>);
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        console.log('Song request updated:', payload);
        onUpdate && onUpdate(payload as RealtimePostgresChangesPayload<SongRequestPayload>);
      })
      .on('postgres_changes', {
        event: 'DELETE',
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        console.log('Song request deleted:', payload);
        onDelete && onDelete(payload as RealtimePostgresChangesPayload<SongRequestPayload>);
      })
      .subscribe((status) => {
        connectionManager.handleSubscriptionStatus(channel, status, 'Session requests');
      });

    // Register the channel with the connection manager
    connectionManager.registerChannel(`session-requests-${sessionId}`, channel);

    return channel;
  } catch (error) {
    console.error(`Error setting up session requests channel for ${sessionId}:`, error);
    throw error;
  }
};

/**
 * Subscribe to updates for a specific request with improved error handling
 */
export const subscribeToRequestUpdates = (
  requestId: string,
  onUpdate?: RealtimeCallback<SongRequestPayload>
): RealtimeChannel => {
  console.log(`Subscribing to request updates for request ${requestId}`);

  const channelName = createUniqueChannelId(`request-updates-${requestId}`);

  try {
    // Clean up any existing channel with the same base ID
    const existingChannels = Array.from(supabase.getChannels()).filter(
      ch => ch.topic.startsWith(`request-updates-${requestId}`)
    );

    for (const channel of existingChannels) {
      try {
        supabase.removeChannel(channel);
      } catch (err) {
        console.error("Error removing existing channel:", err);
      }
    }

    const channel = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'song_requests',
        filter: `id=eq.${requestId}`
      }, (payload) => {
        console.log('Request updated:', payload);
        onUpdate && onUpdate(payload as RealtimePostgresChangesPayload<SongRequestPayload>);
      })
      .subscribe((status) => {
        connectionManager.handleSubscriptionStatus(channel, status, 'Request updates');
      });

    // Register the channel with the connection manager
    connectionManager.registerChannel(`request-updates-${requestId}`, channel);

    return channel;
  } catch (error) {
    console.error(`Error setting up request updates channel for ${requestId}:`, error);
    throw error;
  }
};

/**
 * Subscribe to session status updates with improved error handling
 */
export const subscribeToSessionStatus = (
  sessionId: string,
  onUpdate?: RealtimeCallback<SessionPayload>
): RealtimeChannel => {
  console.log(`🔌 Subscribing to session status for session ${sessionId}`);

  const channelName = createUniqueChannelId(`session-status-${sessionId}`);

  try {
    // Clean up any existing channel with the same base ID
    const existingChannels = Array.from(supabase.getChannels()).filter(
      ch => ch.topic.startsWith(`session-status-${sessionId}`)
    );

    if (existingChannels.length > 0) {
      console.log(`🧹 Cleaning up ${existingChannels.length} existing session status channels`);
      for (const channel of existingChannels) {
        try {
          supabase.removeChannel(channel);
        } catch (err) {
          console.error("Error removing existing channel:", err);
        }
      }
    }

    const channel = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'sessions',
        filter: `id=eq.${sessionId}`
      }, (payload) => {
        console.log('Session status updated:', payload);
        onUpdate && onUpdate(payload as RealtimePostgresChangesPayload<SessionPayload>);
      })
      .subscribe((status) => {
        connectionManager.handleSubscriptionStatus(channel, status, 'Session status');
      });

    // Set up reconnection callback for this channel
    connectionManager.setReconnectCallback((channelId) => {
      if (channelId === `session-status-${sessionId}`) {
        console.log('🔄 Reconnecting session status subscription...');
        // The component should handle reconnection by calling this function again
      }
    });

    // Register the channel with the connection manager
    connectionManager.registerChannel(`session-status-${sessionId}`, channel);

    return channel;
  } catch (error) {
    console.error(`Error setting up session status channel for ${sessionId}:`, error);
    throw error;
  }
};

/**
 * Consolidated subscription to song requests for both status updates and user history
 * This reduces the number of connections by combining multiple subscriptions into one
 */
export const subscribeToConsolidatedRequests = (
  sessionId: string,
  requesterName: string | null,
  onAllUpdates?: RealtimeCallback<SongRequestPayload>,
  onUserUpdates?: RealtimeCallback<SongRequestPayload>,
  onStatusChange?: (songId: string, status: string) => void
): RealtimeChannel => {
  console.log(`🔌 Setting up consolidated request subscription for session ${sessionId} and user ${requesterName || 'any'}`);

  const connectionManager = RealtimeConnectionManager.getInstance();

  // Use a single channel name per session for all audience members
  // Client-side filtering will be done based on requesterName
  const channelName = `session-requests-${sessionId}-audience`;

  try {
    // Clean up any existing channels for this session
    const existingChannels = Array.from(supabase.getChannels()).filter(
      ch => ch.topic.includes(`session-requests-${sessionId}`)
    );

    if (existingChannels.length > 0) {
      console.log(`🧹 Cleaning up ${existingChannels.length} existing consolidated request channels`);
      for (const channel of existingChannels) {
        try {
          supabase.removeChannel(channel);
        } catch (err) {
          console.error("Error removing existing channel:", err);
        }
      }
    }

    const channel = supabase
      .channel(channelName)
      // Listen for all changes in the session
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        // Handle all updates for the entire session
        console.log('Session-wide request update:', payload);

        // Cast the payload to our expected type to handle type checking
        const typedPayload = payload as RealtimePostgresChangesPayload<SongRequestPayload>;

        // Call the general updates callback
        onAllUpdates && onAllUpdates(typedPayload);

        // Safely check if the new data exists and has the required properties
        if (typedPayload.new && typeof typedPayload.new === 'object' && 'apple_music_id' in typedPayload.new &&
            typedPayload.new.apple_music_id && 'status' in typedPayload.new && onStatusChange) {
          console.log(`Updating status for song ${typedPayload.new.apple_music_id} to ${typedPayload.new.status}`);
          onStatusChange(typedPayload.new.apple_music_id, typedPayload.new.status);
        }

        // Client-side filtering for user-specific updates
        if (requesterName && (
            (typedPayload.new && typeof typedPayload.new === 'object' &&
             'requester_name' in typedPayload.new && typedPayload.new.requester_name === requesterName) ||
            (typedPayload.old && typeof typedPayload.old === 'object' &&
             'requester_name' in typedPayload.old && typedPayload.old.requester_name === requesterName))) {
          console.log(`User-specific request update for ${requesterName}:`, typedPayload);
          onUserUpdates && onUserUpdates(typedPayload);
        }
      })
      .subscribe((status) => {
        connectionManager.handleSubscriptionStatus(channel, status, 'Consolidated requests');
      });

    // Set up reconnection callback for this channel
    connectionManager.setReconnectCallback((channelId) => {
      if (channelId === `session-requests-${sessionId}-audience`) {
        console.log('🔄 Reconnecting consolidated requests subscription...');
        // The component should handle reconnection by calling this function again
      }
    });

    return channel;
  } catch (error) {
    console.error(`❌ Error setting up consolidated requests channel for session ${sessionId}:`, error);
    throw error;
  }
};

/**
 * Unsubscribe from a channel with improved error handling
 */
export const unsubscribeFromChannel = (channel: RealtimeChannel): void => {
  if (!channel) return;

  try {
    console.log('Unsubscribing from channel:', asExtendedChannel(channel).topic);
    // First check if the channel exists in the list of active channels
    const activeChannel = supabase.getChannels().find(ch =>
      asExtendedChannel(ch).topic === asExtendedChannel(channel).topic
    );

    if (activeChannel) {
      supabase.removeChannel(activeChannel);
    } else {
      console.log('Channel not found in active channels list, skipping removal');
    }
  } catch (error) {
    console.error(`Error unsubscribing from channel ${asExtendedChannel(channel).topic}:`, error);
  }
};

/**
 * Unsubscribe from multiple channels with improved error handling
 */
export const unsubscribeFromChannels = (channels: RealtimeChannel[]): void => {
  if (!channels || !channels.length) return;

  console.log(`Unsubscribing from ${channels.length} channels`);

  for (const channel of channels) {
    try {
      // First check if the channel exists in the list of active channels
      if (channel) {
        const activeChannel = supabase.getChannels().find(ch =>
          asExtendedChannel(ch).topic === asExtendedChannel(channel).topic
        );

        if (activeChannel) {
          unsubscribeFromChannel(activeChannel);
        }
      }
    } catch (error) {
      console.error(`Error during batch channel unsubscription:`, error);
      // Continue with other channels even if one fails
    }
  }
};

/**
 * Clean up all connections managed by the connection manager
 */
export const cleanupAllConnections = (): void => {
  connectionManager.cleanupAllChannels();
};
