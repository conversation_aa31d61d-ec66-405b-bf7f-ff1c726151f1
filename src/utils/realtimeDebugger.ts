import { supabase } from '@/integrations/supabase/client';
import RealtimeConnectionManager from './realtimeConnectionManager';

/**
 * Debug utility for monitoring Supabase realtime connections
 */
export class RealtimeDebugger {
  private static instance: RealtimeDebugger;
  private logEnabled = process.env.NODE_ENV === 'development';

  private constructor() {}

  public static getInstance(): RealtimeDebugger {
    if (!RealtimeDebugger.instance) {
      RealtimeDebugger.instance = new RealtimeDebugger();
    }
    return RealtimeDebugger.instance;
  }

  /**
   * Log current connection status
   */
  public logConnectionStatus(): void {
    if (!this.logEnabled) return;

    const channels = supabase.getChannels();
    const connectionManager = RealtimeConnectionManager.getInstance();
    
    console.group('🔍 Realtime Connection Status');
    console.log('📊 Total Supabase channels:', channels.length);
    console.log('📊 Managed channels:', connectionManager.getActiveChannelCount());
    
    if (channels.length > 0) {
      console.log('📋 Active channels:');
      channels.forEach((channel, index) => {
        const extendedChannel = channel as any;
        console.log(`  ${index + 1}. ${extendedChannel.topic} (state: ${extendedChannel.state})`);
      });
    }

    console.log('🔧 Manager debug info:', connectionManager.getDebugInfo());
    console.groupEnd();
  }

  /**
   * Monitor WebSocket connection events
   */
  public startMonitoring(): void {
    if (!this.logEnabled) return;

    console.log('🔍 Starting realtime connection monitoring...');
    
    // Log status every 30 seconds
    setInterval(() => {
      this.logConnectionStatus();
    }, 30000);

    // Log when page visibility changes
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        console.log(`👁️ Page visibility changed to: ${document.visibilityState}`);
        if (document.visibilityState === 'visible') {
          setTimeout(() => this.logConnectionStatus(), 1000);
        }
      });
    }
  }

  /**
   * Check for potential connection issues
   */
  public checkForIssues(): string[] {
    const issues: string[] = [];
    const channels = supabase.getChannels();
    const connectionManager = RealtimeConnectionManager.getInstance();

    // Check for too many connections
    if (channels.length > 10) {
      issues.push(`⚠️ High number of WebSocket channels: ${channels.length}. Consider consolidating subscriptions.`);
    }

    // Check for duplicate channel names
    const channelNames = channels.map((ch: any) => ch.topic);
    const duplicates = channelNames.filter((name, index) => channelNames.indexOf(name) !== index);
    if (duplicates.length > 0) {
      issues.push(`⚠️ Duplicate channel names detected: ${[...new Set(duplicates)].join(', ')}`);
    }

    // Check for channels in error state
    const errorChannels = channels.filter((ch: any) => ch.state === 'errored' || ch.state === 'closed');
    if (errorChannels.length > 0) {
      issues.push(`❌ Channels in error state: ${errorChannels.length}`);
    }

    return issues;
  }

  /**
   * Get connection health report
   */
  public getHealthReport(): object {
    const channels = supabase.getChannels();
    const connectionManager = RealtimeConnectionManager.getInstance();
    const issues = this.checkForIssues();

    return {
      timestamp: new Date().toISOString(),
      totalChannels: channels.length,
      managedChannels: connectionManager.getActiveChannelCount(),
      channelStates: channels.reduce((acc: any, ch: any) => {
        acc[ch.state] = (acc[ch.state] || 0) + 1;
        return acc;
      }, {}),
      issues: issues,
      healthy: issues.length === 0 && channels.length <= 10,
      managerDebugInfo: connectionManager.getDebugInfo()
    };
  }

  /**
   * Force cleanup of all connections (for debugging)
   */
  public forceCleanup(): void {
    if (!this.logEnabled) return;

    console.log('🧹 Force cleaning up all realtime connections...');
    
    const connectionManager = RealtimeConnectionManager.getInstance();
    connectionManager.unsubscribeAll();
    
    // Also clean up any remaining Supabase channels
    const channels = supabase.getChannels();
    channels.forEach(channel => {
      try {
        supabase.removeChannel(channel);
      } catch (error) {
        console.error('Error removing channel during force cleanup:', error);
      }
    });

    console.log('✅ Force cleanup completed');
    setTimeout(() => this.logConnectionStatus(), 1000);
  }
}

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
  const debugger = RealtimeDebugger.getInstance();
  debugger.startMonitoring();
  
  // Make debugger available globally for manual debugging
  if (typeof window !== 'undefined') {
    (window as any).realtimeDebugger = debugger;
  }
}

export default RealtimeDebugger;
