import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, REALTIME_SUBSCRIBE_STATES } from '@supabase/supabase-js';

interface ChannelConfig {
  channelName: string;
  table: string;
  event: string;
  filter?: string;
  callback: (payload: any) => void;
}

/**
 * Centralized realtime connection manager to prevent multiple WebSocket connections
 * and handle connection failures gracefully
 */
class RealtimeConnectionManager {
  private static instance: RealtimeConnectionManager;
  private channels: Map<string, RealtimeChannel> = new Map();
  private channelStates: Map<string, string> = new Map();
  private pendingSubscriptions: Map<string, NodeJS.Timeout> = new Map();
  private retryTimers: Map<string, NodeJS.Timeout> = new Map();
  private retryAttempts: Map<string, number> = new Map();
  private readonly MAX_RETRIES = 5;
  private readonly BASE_RETRY_DELAY = 1000;
  private readonly MAX_RETRY_DELAY = 30000;
  private readonly SUBSCRIPTION_DELAY = 100; // Delay to prevent rapid subscribe/unsubscribe

  private constructor() {
    // Listen for page visibility changes to reconnect when page becomes visible
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
  }

  public static getInstance(): RealtimeConnectionManager {
    if (!RealtimeConnectionManager.instance) {
      RealtimeConnectionManager.instance = new RealtimeConnectionManager();
    }
    return RealtimeConnectionManager.instance;
  }

  /**
   * Subscribe to a realtime channel with automatic retry logic and debouncing
   */
  public subscribe(config: ChannelConfig): string {
    const { channelName, table, event, filter, callback } = config;

    // Cancel any pending subscription for this channel
    const pendingTimer = this.pendingSubscriptions.get(channelName);
    if (pendingTimer) {
      clearTimeout(pendingTimer);
      this.pendingSubscriptions.delete(channelName);
    }

    // If channel already exists, check its state before removing
    if (this.channels.has(channelName)) {
      const existingChannel = this.channels.get(channelName);
      const channelState = (existingChannel as any)?.state;

      console.log(`🔌 Channel ${channelName} already exists with state: ${channelState}`);

      // Only remove if it's in a stable state
      if (channelState === 'subscribed' || channelState === 'closed' || channelState === 'errored') {
        this.unsubscribe(channelName);
      } else {
        // Channel is still connecting, wait a bit before trying again
        console.log(`⏳ Channel ${channelName} is still connecting, delaying new subscription`);
        const delayTimer = setTimeout(() => {
          this.subscribe(config);
        }, this.SUBSCRIPTION_DELAY);
        this.pendingSubscriptions.set(channelName, delayTimer);
        return channelName;
      }
    }

    console.log(`🔌 Setting up realtime subscription: ${channelName}`);

    // Set initial state
    this.channelStates.set(channelName, 'connecting');

    const channel = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: event as any,
        schema: 'public',
        table,
        filter
      }, callback)
      .subscribe((status) => {
        this.channelStates.set(channelName, status);
        this.handleSubscriptionStatus(channelName, status);
      });

    this.channels.set(channelName, channel);
    this.retryAttempts.set(channelName, 0);

    return channelName;
  }

  /**
   * Unsubscribe from a channel with proper state checking
   */
  public unsubscribe(channelName: string): void {
    // Cancel any pending subscription
    const pendingTimer = this.pendingSubscriptions.get(channelName);
    if (pendingTimer) {
      clearTimeout(pendingTimer);
      this.pendingSubscriptions.delete(channelName);
      console.log(`🔌 Cancelled pending subscription for: ${channelName}`);
    }

    const channel = this.channels.get(channelName);
    if (channel) {
      const channelState = this.channelStates.get(channelName) || 'unknown';
      console.log(`🔌 Unsubscribing from: ${channelName} (state: ${channelState})`);

      try {
        // Only try to remove if the channel is in a stable state
        if (channelState === 'connecting') {
          console.log(`⚠️ Channel ${channelName} is still connecting, marking for delayed cleanup`);
          // Set a timeout to clean up later when connection is established or failed
          setTimeout(() => {
            const currentState = this.channelStates.get(channelName);
            if (currentState && currentState !== 'connecting') {
              console.log(`🔌 Delayed cleanup for ${channelName} (final state: ${currentState})`);
              try {
                supabase.removeChannel(channel);
              } catch (error) {
                console.error(`Error in delayed cleanup for ${channelName}:`, error);
              }
            }
          }, 1000);
        } else {
          supabase.removeChannel(channel);
        }
      } catch (error) {
        console.error(`Error removing channel ${channelName}:`, error);
      }

      this.channels.delete(channelName);
    }

    // Clear all associated data
    this.channelStates.delete(channelName);

    const timer = this.retryTimers.get(channelName);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(channelName);
    }

    this.retryAttempts.delete(channelName);
  }

  /**
   * Unsubscribe from all channels
   */
  public unsubscribeAll(): void {
    console.log('🔌 Unsubscribing from all realtime channels');

    // Cancel all pending subscriptions first
    for (const [, timer] of this.pendingSubscriptions.entries()) {
      clearTimeout(timer);
    }
    this.pendingSubscriptions.clear();

    // Then unsubscribe from all active channels
    for (const channelName of this.channels.keys()) {
      this.unsubscribe(channelName);
    }
  }

  /**
   * Get the current number of active channels
   */
  public getActiveChannelCount(): number {
    return this.channels.size;
  }

  /**
   * Handle subscription status changes
   */
  private handleSubscriptionStatus(channelName: string, status: string): void {
    console.log(`📡 Channel ${channelName} status: ${status}`);

    switch (status) {
      case REALTIME_SUBSCRIBE_STATES.SUBSCRIBED:
        // Reset retry attempts on successful subscription
        this.retryAttempts.set(channelName, 0);
        break;

      case REALTIME_SUBSCRIBE_STATES.TIMED_OUT:
      case REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR:
        this.handleConnectionError(channelName);
        break;

      case REALTIME_SUBSCRIBE_STATES.CLOSED:
        console.log(`📡 Channel ${channelName} closed`);
        break;
    }
  }

  /**
   * Handle connection errors with exponential backoff retry
   */
  private handleConnectionError(channelName: string): void {
    const attempts = this.retryAttempts.get(channelName) || 0;
    
    if (attempts >= this.MAX_RETRIES) {
      console.error(`❌ Max retry attempts reached for channel: ${channelName}`);
      return;
    }

    const nextAttempt = attempts + 1;
    this.retryAttempts.set(channelName, nextAttempt);

    // Calculate exponential backoff with jitter
    const baseDelay = Math.min(
      this.BASE_RETRY_DELAY * Math.pow(2, attempts),
      this.MAX_RETRY_DELAY
    );
    const jitter = Math.random() * 0.3 * baseDelay;
    const retryDelay = Math.floor(baseDelay + jitter);

    console.log(`🔄 Retrying connection for ${channelName} in ${retryDelay}ms (attempt ${nextAttempt}/${this.MAX_RETRIES})`);

    const timer = setTimeout(() => {
      this.retryConnection(channelName);
    }, retryDelay);

    this.retryTimers.set(channelName, timer);
  }

  /**
   * Retry connection for a specific channel
   */
  private retryConnection(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (!channel) {
      console.log(`⚠️ Channel ${channelName} not found for retry`);
      return;
    }

    console.log(`🔄 Retrying connection for: ${channelName}`);
    
    // Remove the old channel
    try {
      supabase.removeChannel(channel);
    } catch (error) {
      console.error(`Error removing channel during retry:`, error);
    }

    // The channel will be recreated by the component that originally subscribed
    this.channels.delete(channelName);
  }

  /**
   * Handle page visibility changes to reconnect when page becomes visible
   */
  private handleVisibilityChange(): void {
    if (document.visibilityState === 'visible') {
      console.log('📡 Page became visible, checking realtime connections');
      
      // Check if we have any channels that might need reconnection
      if (this.channels.size === 0) {
        console.log('📡 No active channels to reconnect');
        return;
      }

      // Trigger a gentle reconnection check after a short delay
      setTimeout(() => {
        this.checkConnectionHealth();
      }, 1000);
    }
  }

  /**
   * Check the health of all connections
   */
  private checkConnectionHealth(): void {
    console.log('📡 Checking connection health for all channels');
    
    for (const [channelName, channel] of this.channels.entries()) {
      // Check if channel is in a good state
      const channelState = (channel as any).state;
      if (channelState === 'closed' || channelState === 'errored') {
        console.log(`📡 Channel ${channelName} needs reconnection (state: ${channelState})`);
        this.retryConnection(channelName);
      }
    }
  }

  /**
   * Get debug information about current connections
   */
  public getDebugInfo(): object {
    return {
      activeChannels: Array.from(this.channels.keys()),
      retryAttempts: Object.fromEntries(this.retryAttempts),
      activeTimers: this.retryTimers.size
    };
  }
}

export default RealtimeConnectionManager;
