import { supabase } from '@/integrations/supabase/client';

/**
 * Simple utility to test WebSocket connection health
 */
export const testWebSocketConnections = () => {
  if (typeof window === 'undefined') return;

  console.group('🔍 WebSocket Connection Test');
  
  const channels = supabase.getChannels();
  console.log(`📊 Total active channels: ${channels.length}`);
  
  if (channels.length > 0) {
    console.log('📋 Channel details:');
    channels.forEach((channel, index) => {
      const extendedChannel = channel as any;
      console.log(`  ${index + 1}. Topic: ${extendedChannel.topic}`);
      console.log(`     State: ${extendedChannel.state}`);
    });
  }

  // Check for potential issues
  const issues: string[] = [];

  if (channels.length > 10) {
    issues.push(`⚠️ High number of channels: ${channels.length}`);
  }

  const errorChannels = channels.filter((ch: any) =>
    ch.state === 'errored' || ch.state === 'closed'
  );

  if (errorChannels.length > 0) {
    issues.push(`❌ Channels in error state: ${errorChannels.length}`);
  }

  const connectingChannels = channels.filter((ch: any) =>
    ch.state === 'connecting'
  );

  if (connectingChannels.length > 0) {
    console.log(`⏳ Channels still connecting: ${connectingChannels.length}`);
    connectingChannels.forEach((ch: any) => {
      console.log(`  - ${ch.topic} (connecting)`);
    });
  }

  if (issues.length > 0) {
    console.warn('🚨 Issues detected:');
    issues.forEach(issue => console.warn(`  ${issue}`));
  } else {
    console.log('✅ No issues detected');
  }
  
  console.groupEnd();
  
  return {
    totalChannels: channels.length,
    issues: issues,
    healthy: issues.length === 0 && channels.length <= 10
  };
};

// Make available globally for manual testing
if (typeof window !== 'undefined') {
  (window as any).testWebSocketConnections = testWebSocketConnections;
}
