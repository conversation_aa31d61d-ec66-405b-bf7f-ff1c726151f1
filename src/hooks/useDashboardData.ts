import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { supabase } from '@/integrations/supabase/client';
import { SessionWithSettings } from '@/types/session';
import RealtimeConnectionManager from '@/utils/realtimeConnectionManager';

interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  created_at: string;
  updated_at: string;
}

export function useDashboardData(userId?: string) {
  const [sessions, setSessions] = useState<SessionWithSettings[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController>();
  const connectionManager = useRef(RealtimeConnectionManager.getInstance());
  const subscribedChannelsRef = useRef<string[]>([]);
  const subscriptionTimeoutRef = useRef<NodeJS.Timeout>();
  const isSubscribedRef = useRef(false);

  // Only log once when userId changes and track mount cycles
  const hasLoggedRef = useRef<string | undefined>();
  const mountCountRef = useRef(0);

  if (hasLoggedRef.current !== userId) {
    mountCountRef.current = 0;
    hasLoggedRef.current = userId;
  }

  mountCountRef.current++;

  if (process.env.NODE_ENV === 'development') {
    console.log(`✅ DASHBOARD_DATA: Hook initialized with userId: ${userId} (mount #${mountCountRef.current}${mountCountRef.current > 1 ? ' - React StrictMode?' : ''})`);
  }

  const fetchSessionsOnly = useCallback(async () => {
    if (!userId) return;

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const sessionsResult = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', userId)
        .order('created_at', { ascending: false });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;

      // Update sessions state only
      setSessions(sessionsResult.data || []);
    } catch (err) {
      console.error('Failed to fetch sessions:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch sessions'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  const fetchAllData = useCallback(async () => {
    if (!userId) return;

    console.log('✅ DASHBOARD_DATA: fetchAllData called for userId:', userId);

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      // Fetch sessions and subscriptions in parallel
      const [sessionsResult, subscriptionsResult] = await Promise.all([
        supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', userId)
          .order('created_at', { ascending: false }),
        supabase
          .from('dj_subscriptions')
          .select('*')
          .eq('dj_id', userId)
      ]);

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;
      if (subscriptionsResult.error) throw subscriptionsResult.error;

      // Update state with new data
      setSessions(sessionsResult.data || []);
      setSubscriptions(subscriptionsResult.data || []);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  // Fetch data and set up subscriptions when userId changes with debouncing
  useEffect(() => {
    if (!userId) {
      isSubscribedRef.current = false;
      return;
    }

    // Clear any pending subscription setup
    if (subscriptionTimeoutRef.current) {
      clearTimeout(subscriptionTimeoutRef.current);
    }

    console.log('✅ DASHBOARD_DATA: Setting up data fetch and subscriptions for userId:', userId);

    // Fetch initial data immediately
    fetchAllData();

    // Debounce subscription setup to prevent rapid subscribe/unsubscribe cycles
    subscriptionTimeoutRef.current = setTimeout(() => {
      // Double-check that we still have the same userId and haven't been cleaned up
      if (!userId || isSubscribedRef.current) {
        return;
      }

      console.log('✅ DASHBOARD_DATA: Setting up debounced subscriptions for userId:', userId);
      isSubscribedRef.current = true;

      // Set up realtime subscriptions using the centralized manager
      const sessionChannelName = connectionManager.current.subscribe({
        channelName: `dashboard-session-changes-${userId}`,
        table: 'sessions',
        event: '*',
        filter: `dj_id=eq.${userId}`,
        callback: (payload) => {
          console.log('✅ Session change detected, refreshing data');
          fetchAllData();
        }
      });

      const subscriptionChannelName = connectionManager.current.subscribe({
        channelName: `dashboard-subscription-changes-${userId}`,
        table: 'dj_subscriptions',
        event: '*',
        filter: `dj_id=eq.${userId}`,
        callback: (payload) => {
          console.log('✅ Subscription change detected, refreshing data');
          fetchAllData();
        }
      });

      // Store channel names for cleanup
      subscribedChannelsRef.current = [sessionChannelName, subscriptionChannelName];
    }, 200); // 200ms debounce delay

    return () => {
      console.log('✅ DASHBOARD_DATA: Cleaning up subscriptions for userId:', userId);

      // Clear pending subscription setup
      if (subscriptionTimeoutRef.current) {
        clearTimeout(subscriptionTimeoutRef.current);
        subscriptionTimeoutRef.current = undefined;
      }

      // Clean up existing subscriptions
      subscribedChannelsRef.current.forEach(channelName => {
        connectionManager.current.unsubscribe(channelName);
      });
      subscribedChannelsRef.current = [];
      isSubscribedRef.current = false;
    };
  }, [userId, fetchAllData]); // Include fetchAllData since it's stable with useCallback

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    sessions,
    subscriptions,
    loading,
    error,
    fetchAllData,
    fetchSessionsOnly,
  };
}
