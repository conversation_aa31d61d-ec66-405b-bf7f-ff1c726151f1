
import { RouterProvider } from 'react-router-dom';
import { ThemeProvider } from '@/context/ThemeContext';
import { MusicErrorBoundary } from '@/components/error/MusicErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import CookieConsentManager from '@/components/cookie/CookieConsentManager';
import router from './routes';
import { useEffect } from 'react';
import { initAnalytics } from '@/utils/simpleAnalytics';
import { HelmetProvider } from 'react-helmet-async';
import { useAuthStore, initializeAuthListener } from '@/stores/authStore';
import { testWebSocketConnections } from '@/utils/connectionTest';

export default function App() {
  const initialize = useAuthStore(state => state.initialize);

  // Initialize auth store and analytics
  useEffect(() => {
    // Initialize auth state from Supabase (synchronous)
    initialize();

    // Set up auth listener (synchronous)
    const subscription = initializeAuthListener();

    // Initialize analytics asynchronously (don't block auth)
    initAnalytics().catch((error) => {
      console.log('Analytics initialization failed, continuing without analytics');
    });

    // Test WebSocket connections after a short delay
    setTimeout(() => {
      testWebSocketConnections();
    }, 3000);

    return () => {
      subscription?.unsubscribe();
    };
  }, [initialize]);

  return (
    <HelmetProvider>
      <ThemeProvider>
        <MusicErrorBoundary>
          <RouterProvider router={router} />
        </MusicErrorBoundary>
        <Toaster />
        <CookieConsentManager />
      </ThemeProvider>
    </HelmetProvider>
  );
}
