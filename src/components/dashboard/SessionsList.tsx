
import { useState, useCallback, useEffect, useRef } from "react";
import { SessionWithSettings } from "@/types/session";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { ChevronRight, ChevronDown, Trash2, Calendar, Music, AlertCircle, Settings, Settings2, Play, QrCode, Clock, Plus, AlertTriangle } from "lucide-react";
import { motion } from "framer-motion";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { deleteAppleMusicPlaylist } from "@/utils/appleMusicService";
import { TimeRemainingDisplay } from "@/components/ui/time-remaining-display";
import { formatDurationObj } from "@/hooks/use-time-remaining";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import QRCodeGenerator from "@/components/sessions/QRCodeGenerator";
import PlaylistManager from "@/components/sessions/PlaylistManager";
import SessionSettings from "@/components/sessions/SessionSettings";
import { useSessionData } from "@/hooks/useSessionData";
import SongRequestsView from "@/components/sessions/SongRequestsView";
import RealtimeConnectionManager from "@/utils/realtimeConnectionManager";

interface SessionsListProps {
  sessions: SessionWithSettings[];
  onSelectSession: (sessionId: string) => void;
  loading?: boolean;
  viewMode?: string | null;
  onViewModeChange?: (view: string | null) => void;
  onSessionDeleted?: () => void; // Add callback for when session is deleted
}

export function SessionsList({ sessions, onSelectSession, loading = false, viewMode, onViewModeChange, onSessionDeleted }: SessionsListProps) {
  const { toast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [sessionRequestCounts, setSessionRequestCounts] = useState<Record<string, number>>({});
  const [freeTierSessions, setFreeTierSessions] = useState<Record<string, boolean>>({});
  const [expandedSessionId, setExpandedSessionId] = useState<string | null>(null);
  const [showExpiredSessions, setShowExpiredSessions] = useState(false);

  // Extract session ID and view type from viewMode (format: "sessionId:viewType")
  const currentSessionId = viewMode?.includes(':') ? viewMode.split(':')[0] : null;
  const currentViewType = viewMode?.includes(':') ? viewMode.split(':')[1] : null;

  // Helper functions to manage view state
  const showSongRequests = (sessionId: string) => {
    onViewModeChange?.(sessionId + ':requests');
  };

  const showSettings = (sessionId: string) => {
    onViewModeChange?.(sessionId + ':settings');
  };

  const closeView = () => {
    onViewModeChange?.(null);
  };
  const [selectedPlaylistId, setSelectedPlaylistId] = useState<string | null>(null);

  // Calculate remaining time function
  const calculateSessionRemainingTime = useCallback((session: SessionWithSettings): number => {
    if (!session.created_at || !session.duration_minutes) return 0;

    const startTime = new Date(session.created_at).getTime();
    const durationMs = session.duration_minutes * 60 * 1000;
    const endTime = startTime + durationMs;
    const now = new Date().getTime();

    const remainingMs = endTime - now;
    return Math.max(0, Math.floor(remainingMs / (60 * 1000))); // Convert to minutes and ensure non-negative
  }, []);

  // Get color for progress bar based on request count and tier
  const getProgressColor = (count: number, sessionId: string) => {
    const maxRequests = freeTierSessions[sessionId] ? 3 : 100;
    const percentage = (count / maxRequests) * 100;
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // We now use the formatDuration from our shared hook

  // Check if there's an active session
  const hasActiveSession = sessions.some(session => session.active === true);

  // Separate active and expired sessions
  const activeSessions = sessions.filter(session => session.active === true);
  const expiredSessions = sessions.filter(session => session.active === false);

  // Helper component for rendering session cards
  const SessionCard = ({ session, isMobile = false }: { session: SessionWithSettings, isMobile?: boolean }) => (
    <motion.div
      key={session.id}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={isMobile ? "mb-4" : ""}
    >
      <div className={`${isMobile ? 'bg-card' : 'bg-gradient-to-br from-gray-900/60 to-gray-800/30 border border-gray-700/50'} rounded-lg shadow-sm overflow-hidden hover:bg-gray-800/50 transition-all duration-200 ${!isMobile ? 'hover:shadow-lg group' : ''}`}>
        <div className={isMobile ? "p-4" : "p-5"}>
          {/* Session Header with Title */}
          <div className={`flex ${isMobile ? 'justify-between items-center' : 'flex-col'} mb-${isMobile ? '3' : '4'}`}>
            <h3 className={`${isMobile ? 'text-xl' : 'text-xl'} font-${isMobile ? 'bold' : 'semibold'} text-white ${isMobile ? 'truncate' : 'mb-2'}`}>
              {session.name || `Session ${session.id.slice(0, 8)}`}
            </h3>

            {/* Status Badges */}
            {isMobile ? (
              session.active ? (
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  Active
                </Badge>
              ) : (
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  Expired
                </Badge>
              )
            ) : (
              <div className="flex flex-wrap gap-2 mb-1">
                {session.active ? (
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30 py-1 flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    Active
                  </Badge>
                ) : (
                  <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 py-1 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Expired
                  </Badge>
                )}
                {freeTierSessions[session.id] && (
                  <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 py-1">
                    Free Tier
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Request Count Section */}
          {!isMobile && (
            <div className="mb-5 bg-black/20 p-3 rounded-lg border border-gray-700/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="bg-purple-500/10 p-1.5 rounded-full mr-2">
                    <Music className="h-4 w-4 text-purple-400" />
                  </div>
                  <span className="text-sm font-medium text-gray-300">
                    {sessionRequestCounts[session.id] || 0}/{freeTierSessions[session.id] ? '3' : '100'} Requests
                  </span>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <AlertCircle className="h-3.5 w-3.5 text-gray-500 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700">
                      <p>
                        {freeTierSessions[session.id] ?
                          "Free tier sessions are limited to 3 song requests total. Upgrade to a paid plan for up to 100 requests per session." :
                          "All paid tier sessions have a maximum limit of 100 song requests to ensure system stability and fair resource allocation."}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {/* Progress bar */}
              <div className="w-full bg-gray-800 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(sessionRequestCounts[session.id] || 0, session.id)}`}
                  style={{ width: `${Math.min(((sessionRequestCounts[session.id] || 0) / (freeTierSessions[session.id] ? 3 : 100)) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Mobile simplified request info */}
          {isMobile && (
            <div className="flex justify-between items-start text-sm mb-2">
              <div className="flex flex-col space-y-1">
                <div className="flex items-center">
                  <span className="text-gray-400">Requests: </span>
                  <span className="font-medium ml-1">
                    {sessionRequestCounts[session.id] || 0}/{freeTierSessions[session.id] ? '3' : '100'}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  Created {formatDistanceToNow(new Date(session.created_at))} ago
                </div>
              </div>
              {freeTierSessions[session.id] && (
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 text-xs">
                  Free Tier
                </Badge>
              )}
            </div>
          )}

          {/* Progress bar for mobile */}
          {isMobile && (
            <div className="mb-4">
              <div className="w-full bg-gray-800 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(sessionRequestCounts[session.id] || 0, session.id)}`}
                  style={{ width: `${Math.min(((sessionRequestCounts[session.id] || 0) / (freeTierSessions[session.id] ? 3 : 100)) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Session Metadata for desktop */}
          {!isMobile && (
            <div className="bg-black/10 rounded-lg p-3 mb-5 border border-gray-700/20">
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center">
                  <div className="bg-gray-700/30 p-1.5 rounded-full mr-2">
                    <Calendar className="h-3.5 w-3.5 text-gray-300" />
                  </div>
                  <span className="text-sm text-gray-400">
                    Created {formatDistanceToNow(new Date(session.created_at), { addSuffix: true })}
                  </span>
                </div>
                {session.duration_minutes && (
                  <div className="flex items-center">
                    <TimeRemainingDisplay
                      createdAt={session.created_at}
                      durationMinutes={session.duration_minutes}
                      sessionId={session.id}
                      compact={true}
                      iconSize="sm"
                    />
                    {calculateSessionRemainingTime(session) > 0 && (
                      <span className="text-sm text-gray-400 ml-1">
                        remaining
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-end space-x-3'} mt-4`}>
            <Button
              size="sm"
              className={`bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold px-4 transition-colors duration-200 ${isMobile ? 'w-full min-h-[44px] active:scale-[0.98] active:opacity-90' : ''}`}
              onClick={() => showSongRequests(session.id)}
            >
              <Play className="h-4 w-4 mr-1" />
              <span>Song Requests</span>
            </Button>
            <Button
              size="sm"
              onClick={() => showSettings(session.id)}
              className={`bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold px-4 transition-colors duration-200 ${isMobile ? 'w-full min-h-[44px] active:scale-[0.98] active:opacity-90' : ''}`}
            >
              <Settings2 className="h-4 w-4 mr-1" />
              <span>Settings</span>
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-red-900/30 bg-red-950/20 hover:bg-red-950/40 text-red-400 transition-all duration-200 ${isMobile ? 'w-full min-h-[44px] active:scale-[0.98] active:opacity-90 touch-manipulation' : ''}`}
                  disabled={isDeleting && sessionToDelete === session.id}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  <span>{isDeleting && sessionToDelete === session.id ? "Deleting..." : "Delete"}</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className={`bg-gray-900 border-gray-700 ${isMobile ? 'max-w-[95vw] mx-4' : ''}`}>
                <AlertDialogHeader>
                  <AlertDialogTitle className="text-white">Delete Session</AlertDialogTitle>
                  <AlertDialogDescription className="text-gray-400">
                    Are you sure you want to delete "{session.name || `Session ${session.id.slice(0, 8)}`}"? This action cannot be undone.
                    All song requests will be permanently deleted from PlayBeg, and the associated Apple Music playlist will also be deleted from your Apple Music library.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className={isMobile ? 'flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2' : ''}>
                  <AlertDialogCancel className={`bg-gray-800 text-white hover:bg-gray-700 border-gray-700 ${isMobile ? 'w-full min-h-[44px] order-2 sm:order-1' : ''}`}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    className={`bg-red-600 text-white hover:bg-red-700 transition-all duration-200 ${isMobile ? 'w-full min-h-[44px] order-1 sm:order-2' : ''}`}
                    onClick={() => handleDeleteSession(session.id)}
                    disabled={isDeleting}
                  >
                    {isDeleting && sessionToDelete === session.id ? "Deleting..." : "Delete Session"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </motion.div>
  );

  // Define fetchSessionData outside useEffect so it can be used by other functions
  // Use a ref to avoid dependency on sessions array which changes frequently
  const sessionsRef = useRef(sessions);
  sessionsRef.current = sessions;

  const fetchSessionData = useCallback(async () => {
    const currentSessions = sessionsRef.current;
    if (!currentSessions.length) return;

    try {
      // Process the results
      const counts: Record<string, number> = {};
      const freeTierStatus: Record<string, boolean> = {};

      for (const session of currentSessions) {
        // Get count for this session (excluding archived requests)
        const { count, error } = await supabase
          .from('song_requests')
          .select('id', { count: 'exact', head: true })
          .eq('session_id', session.id)
          .neq('status', 'archived');

        if (!error) {
          counts[session.id] = count || 0;
        }

        // Check if this session is on free tier - always fetch fresh data
        const { data: isFreeTier, error: freeTierError } = await supabase
          .rpc('is_free_tier_session', { session_id: session.id });

        if (!freeTierError) {
          freeTierStatus[session.id] = isFreeTier || false;
          console.log(`Session ${session.id} free tier status: ${isFreeTier}`);
        } else {
          console.error(`Error checking free tier status for session ${session.id}:`, freeTierError);
          // Default to free tier for safety if we can't determine status
          freeTierStatus[session.id] = true;
        }
      }

      setSessionRequestCounts(counts);
      setFreeTierSessions(freeTierStatus);
      console.log('Updated free tier sessions:', freeTierStatus);
    } catch (error) {
      console.error('Error in fetchSessionData:', error);
    }
  }, []); // No dependencies to prevent recreation

  // Fetch request counts for all sessions - only set up subscriptions once
  useEffect(() => {
    if (!sessions.length) return;

    fetchSessionData();
  }, [sessions]);

  // Set up realtime subscriptions once when component mounts using centralized manager
  useEffect(() => {
    const connectionManager = RealtimeConnectionManager.getInstance();
    const subscribedChannels: string[] = [];

    // Create unique channel names to prevent conflicts with other components
    const componentId = `sessions-list-${Date.now()}`;

    // Set up realtime subscription for song request updates
    const requestsChannelName = connectionManager.subscribe({
      channelName: `session-request-counts-${componentId}`,
      table: 'song_requests',
      event: '*',
      callback: () => {
        fetchSessionData();
      }
    });
    subscribedChannels.push(requestsChannelName);

    // Set up realtime subscription for session status changes
    const statusChannelName = connectionManager.subscribe({
      channelName: `session-status-changes-${componentId}`,
      table: 'sessions',
      event: 'UPDATE',
      callback: (payload) => {
        // Only refresh if the active status actually changed to prevent infinite loops
        const oldActive = payload.old?.active;
        const newActive = payload.new?.active;

        if (oldActive !== newActive) {
          console.log('Session active status changed, refreshing data for session:', payload.new?.id, 'from', oldActive, 'to', newActive);
          fetchSessionData();
        } else {
          console.log('Session updated but active status unchanged, skipping refresh for session:', payload.new?.id);
        }
      }
    });
    subscribedChannels.push(statusChannelName);

    // Set up realtime subscription for new sessions
    const newSessionsChannelName = connectionManager.subscribe({
      channelName: `new-sessions-${componentId}`,
      table: 'sessions',
      event: 'INSERT',
      callback: () => {
        // Refresh data when new sessions are created to get correct tier status
        console.log('New session created, refreshing tier status');
        fetchSessionData();
      }
    });
    subscribedChannels.push(newSessionsChannelName);

    // Set up realtime subscription for subscription changes that affect tier status
    const subscriptionChannelName = connectionManager.subscribe({
      channelName: `subscription-tier-changes-${componentId}`,
      table: 'dj_subscriptions',
      event: '*',
      callback: () => {
        // Refresh tier status when subscription changes
        console.log('Subscription changed, refreshing tier status');
        fetchSessionData();
      }
    });
    subscribedChannels.push(subscriptionChannelName);

    return () => {
      // Clean up all subscriptions
      subscribedChannels.forEach(channelName => {
        connectionManager.unsubscribe(channelName);
      });
    };
  }, []); // Empty dependency array - set up once on mount

  // Function to delete a session
  const handleDeleteSession = async (sessionId: string) => {
    try {
      setIsDeleting(true);
      setSessionToDelete(sessionId);

      // First, get session details to find associated playlist
      const { data: sessionData, error: sessionError } = await supabase
        .from('sessions')
        .select('apple_music_playlist_id, dj_id')
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        console.error('Error fetching session data:', sessionError);
        // Continue with session deletion even if we can't get playlist info
      }

      // Try to delete the associated Apple Music playlist if it exists
      if (sessionData?.apple_music_playlist_id && sessionData?.dj_id) {
        console.log(`Attempting to delete Apple Music playlist: ${sessionData.apple_music_playlist_id}`);

        const playlistDeletionResult = await deleteAppleMusicPlaylist(
          sessionData.apple_music_playlist_id,
          sessionData.dj_id
        );

        if (playlistDeletionResult.success) {
          console.log('Successfully deleted Apple Music playlist');
        } else {
          console.warn('Failed to delete Apple Music playlist:', playlistDeletionResult.error);
          // Don't fail the entire operation if playlist deletion fails
        }
      }

      // Delete the session from the database
      const { error } = await supabase
        .from('sessions')
        .delete()
        .eq('id', sessionId);

      if (error) throw error;

      toast({
        title: "Session deleted",
        description: "The session has been permanently deleted.",
      });

      // Immediately trigger parent component refresh
      onSessionDeleted?.();

      // The parent component will also handle the refresh via realtime subscription
      // The UI will update automatically when the parent's sessions state changes

    } catch (error: any) {
      toast({
        title: "Error deleting session",
        description: error.message || "An error occurred while deleting the session.",
        variant: "destructive",
      });
      console.error("Error deleting session:", error);
    } finally {
      setIsDeleting(false);
      setSessionToDelete(null);
    }
  };
  if (loading) {
    return (
      <Card className="modern-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-white">Loading Sessions...</CardTitle>
          <CardDescription className="text-gray-300">
            Please wait while we fetch your sessions
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (sessions.length === 0) {
    return (
      <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md shadow-lg">
        {/* Desktop Header - Only visible on desktop */}
        <CardHeader className="hidden md:block pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-white text-xl font-bold">Your Sessions</CardTitle>
              <CardDescription className="text-gray-300">
                View and manage your DJ sessions
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        {/* Mobile Empty State - Only visible on mobile */}
        <div className="md:hidden px-4 py-6 text-center space-y-4">
          <div className="flex justify-center">
            <Music className="h-12 w-12 text-purple-500/70" />
          </div>
          <h2 className="text-lg font-semibold text-white">No Sessions Found</h2>
          <p className="text-sm text-muted-foreground">
            Create your first DJ session to start accepting song requests from your audience.
          </p>
          <Button
            className="w-full bg-gradient-to-r from-purple-500 to-cyan-500 text-white font-medium shadow-lg hover:shadow-purple-500/20 transition-all duration-300 active:scale-[0.98] active:opacity-90 flex items-center justify-center"
            onClick={() => onSelectSession('new')}
            disabled={hasActiveSession}
            title={hasActiveSession ? "You already have an active session. Please deactivate it before creating a new one." : "Create a new session"}
          >
            <Music className="h-4 w-4 mr-2" />
            Create New Session
          </Button>
          {hasActiveSession && (
            <div className="bg-amber-950/40 border border-amber-700/30 rounded-md p-3 mt-4 text-left">
              <p className="text-amber-400 text-sm flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                You already have an active session. Please deactivate it before creating a new one.
              </p>
            </div>
          )}
        </div>

        {/* Desktop Empty State - Only visible on desktop */}
        <CardContent className="hidden md:block pt-2">
          <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/30 border border-gray-700/50 rounded-lg p-8 text-center shadow-inner">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="w-16 h-16 rounded-full bg-purple-500/20 flex items-center justify-center mb-2">
                <Music className="h-8 w-8 text-purple-400" />
              </div>
              <h3 className="text-white text-lg font-medium">No Sessions Found</h3>
              <p className="text-gray-400 max-w-md">Create your first DJ session to start accepting song requests from your audience.</p>
              <Button
                className="bg-gradient-to-r from-purple-500 to-cyan-500 text-white font-medium px-6 py-2 mt-4 shadow-lg hover:shadow-purple-500/20 transition-all duration-300 transform hover:scale-105"
                onClick={() => onSelectSession('new')}
                disabled={hasActiveSession}
                title={hasActiveSession ? "You already have an active session. Please deactivate it before creating a new one." : "Create a new session"}
              >
                Create New Session
              </Button>
              {hasActiveSession && (
                <div className="bg-amber-950/40 border border-amber-700/30 rounded-md p-3 mt-4 text-left">
                  <p className="text-amber-400 text-sm flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    You already have an active session. Please deactivate it before creating a new one.
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md shadow-lg">
      {/* Desktop Header - Only visible on desktop */}
      <CardHeader className="hidden md:block pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-white text-xl font-bold">Your Session</CardTitle>
            <CardDescription className="text-gray-300">
              View and manage your DJ sessions
            </CardDescription>
          </div>
          <Button
            className="bg-gradient-to-r from-purple-500 to-cyan-500 text-white font-medium shadow-lg hover:shadow-purple-500/20 transition-all duration-300 md:absolute md:top-6 md:right-6 md:w-auto"
            onClick={() => onSelectSession('new')}
            disabled={hasActiveSession}
            title={hasActiveSession ? "You already have an active session. Please deactivate it before creating a new one." : "Create a new session"}
          >
            Create New Session
          </Button>
        </div>
      </CardHeader>

      {/* Mobile Header - Only visible on mobile */}
      <div className="md:hidden flex flex-col space-y-4 px-4 pt-4 mb-4">
        <div className="flex flex-col space-y-3">
          <h2 className="text-xl font-bold text-white">Your Session</h2>
          <p className="text-sm text-muted-foreground mb-2">View and manage your DJ sessions</p>
          <Button
            className="w-full bg-gradient-to-r from-purple-500 to-cyan-500 text-white font-medium shadow-lg hover:shadow-purple-500/20 transition-all duration-300 active:scale-[0.98] active:opacity-90 flex items-center justify-center"
            onClick={() => onSelectSession('new')}
            disabled={hasActiveSession}
            title={hasActiveSession ? "You already have an active session. Please deactivate it before creating a new one." : "Create a new session"}
          >
            <Music className="h-4 w-4 mr-2" />
            Create New Session
          </Button>
        </div>
      </div>
      <CardContent>
        {/* Add a subtle indicator that content is scrollable when there are multiple sessions */}
        {sessions.length > 1 && (
          <div className="flex justify-center mb-2">
            <div className="text-xs text-gray-500 flex items-center">
              <span>Scroll to see all sessions</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1 animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </div>
          </div>
        )}
        {/* Mobile Layout - Organized by Status */}
        <div className="md:hidden flex flex-col px-4 space-y-6">
          {/* Active Sessions Section */}
          {activeSessions.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <h3 className="text-lg font-semibold text-white">Active Session</h3>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                  {activeSessions.length}
                </Badge>
              </div>
              {activeSessions.map((session) => (
                <SessionCard key={session.id} session={session} isMobile={true} />
              ))}
            </div>
          )}

          {/* Expired Sessions Section */}
          {expiredSessions.length > 0 && (
            <div className="space-y-4">
              <button
                onClick={() => setShowExpiredSessions(!showExpiredSessions)}
                className="flex items-center gap-2 w-full text-left group"
              >
                <ChevronDown className={`w-4 h-4 text-amber-400 transition-transform duration-200 ${showExpiredSessions ? 'rotate-180' : ''}`} />
                <h3 className="text-lg font-semibold text-amber-400 group-hover:text-amber-300 transition-colors">
                  Expired Sessions
                </h3>
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 text-xs">
                  {expiredSessions.length}
                </Badge>
              </button>

              {showExpiredSessions && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-4"
                >
                  {expiredSessions.map((session) => (
                    <SessionCard key={session.id} session={session} isMobile={true} />
                  ))}
                </motion.div>
              )}
            </div>
          )}
        </div>

        {/* Desktop Layout - Organized by Status with Vertical Scrolling Only */}
        <div className="hidden md:block space-y-6">
          {/* Active Sessions Section */}
          {activeSessions.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <h3 className="text-lg font-semibold text-white">Active Session</h3>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                  {activeSessions.length}
                </Badge>
              </div>
              <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2 mobile-vertical-scroll-only">
                {activeSessions.map((session) => (
                  <SessionCard key={session.id} session={session} isMobile={false} />
                ))}
              </div>
            </div>
          )}

          {/* Expired Sessions Section */}
          {expiredSessions.length > 0 && (
            <div className="space-y-4">
              <button
                onClick={() => setShowExpiredSessions(!showExpiredSessions)}
                className="flex items-center gap-2 w-full text-left group"
              >
                <ChevronDown className={`w-4 h-4 text-amber-400 transition-transform duration-200 ${showExpiredSessions ? 'rotate-180' : ''}`} />
                <h3 className="text-lg font-semibold text-amber-400 group-hover:text-amber-300 transition-colors">
                  Expired Sessions
                </h3>
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 text-xs">
                  {expiredSessions.length}
                </Badge>
              </button>

              {showExpiredSessions && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-4 max-h-[300px] overflow-y-auto pr-2 mobile-vertical-scroll-only"
                >
                  {expiredSessions.map((session) => (
                    <SessionCard key={session.id} session={session} isMobile={false} />
                  ))}
                </motion.div>
              )}
            </div>
          )}
        </div>

        {/* Administrative Components - Always visible below session list */}
        {sessions.length > 0 && (
          <div className="mt-6 space-y-6">
            {/* Playlist Manager */}
            <Card className="bg-gray-900/70 border border-purple-500/20 backdrop-blur-md">
              <CardHeader className="pb-3 border-b border-purple-500/10">
                <CardTitle className="text-xl text-white">Playlist Manager</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="min-h-[300px]">
                  {sessions.find(s => s.active) ? (
                    <PlaylistManager
                      sessionId={sessions.find(s => s.active)!.id}
                      onPlaylistSelected={setSelectedPlaylistId}
                      className="h-full"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full min-h-[280px] text-center space-y-6">
                      {/* Enhanced Visual Indicator */}
                      <div className="relative">
                        <div className="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-purple-500/30">
                          <Music className="h-10 w-10 text-purple-400" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-500/20 border border-amber-500/40 rounded-full flex items-center justify-center">
                          <Clock className="h-3 w-3 text-amber-400" />
                        </div>
                      </div>

                      {/* Clear Status Communication */}
                      <div className="space-y-3 max-w-md">
                        <h3 className="text-lg font-semibold text-white">No Active Session</h3>
                        <p className="text-gray-400 text-sm leading-relaxed">
                          Playlist management is only available when you have an active session.
                          {sessions.length > 0 ? " Your sessions have expired or been deactivated." : " Create a new session to get started."}
                        </p>
                      </div>

                      {/* Actionable CTAs */}
                      <div className="flex justify-center w-full max-w-sm">
                        <Button
                          onClick={() => window.location.href = '/dashboard'}
                          className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-medium transition-all duration-200 active:scale-[0.98]"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create New Session
                        </Button>
                      </div>

                      {/* Additional Context for Expired Sessions */}
                      {sessions.some(s => !s.active) && (
                        <div className="mt-4 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg max-w-md">
                          <div className="flex items-start space-x-2">
                            <AlertTriangle className="h-4 w-4 text-amber-400 mt-0.5 flex-shrink-0" />
                            <div className="text-xs text-amber-200">
                              <span className="font-medium">Session Expired:</span> Your session has reached its time limit.
                              Create a new session to continue managing playlists and accepting song requests.
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Share Session */}
            <Card className="bg-gray-900/70 border border-purple-500/20 backdrop-blur-md">
              <CardHeader className="pb-3 border-b border-purple-500/10">
                <CardTitle className="text-xl text-white">Share Session</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                {sessions.find(s => s.active) ? (
                  <QRCodeGenerator session={sessions.find(s => s.active)!} />
                ) : (
                  <div className="flex flex-col items-center justify-center text-center space-y-6 py-8">
                    {/* Enhanced Visual Indicator */}
                    <div className="relative">
                      <div className="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-purple-500/30">
                        <QrCode className="h-10 w-10 text-purple-400" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-500/20 border border-amber-500/40 rounded-full flex items-center justify-center">
                        <Clock className="h-3 w-3 text-amber-400" />
                      </div>
                    </div>

                    {/* Clear Status Communication */}
                    <div className="space-y-3 max-w-md">
                      <h3 className="text-lg font-semibold text-white">No Active Session to Share</h3>
                      <p className="text-gray-400 text-sm leading-relaxed">
                        Session sharing is only available when you have an active session running.
                        {sessions.length > 0 ? " Your sessions have expired or been deactivated." : " Create a new session to generate a QR code for song requests."}
                      </p>
                    </div>

                    {/* Actionable CTAs */}
                    <div className="flex justify-center w-full max-w-sm">
                      <Button
                        onClick={() => onSelectSession('new')}
                        className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-medium transition-all duration-200 active:scale-[0.98]"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create New Session
                      </Button>
                    </div>

                    {/* Preview of what they'll get */}
                    <div className="mt-4 p-4 bg-black/30 border border-gray-700/30 rounded-lg max-w-md">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-white/10 rounded border border-gray-600/30 flex items-center justify-center flex-shrink-0">
                          <QrCode className="h-4 w-4 text-gray-400" />
                        </div>
                        <div className="text-xs text-gray-400">
                          <span className="font-medium text-gray-300">Coming Soon:</span> Once you have an active session,
                          you'll get a QR code and shareable link that your audience can use to request songs in real-time.
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>

      {/* Song Requests View Modal */}
      {currentViewType === 'requests' && currentSessionId && (
        <div className="fixed inset-0 z-50 bg-black">
          <SongRequestsView
            sessionId={currentSessionId}
            sessionName={sessions.find(s => s.id === currentSessionId)?.name || 'Untitled Session'}
            onBack={closeView}
          />
        </div>
      )}

      {/* Settings Dialog */}
      <Dialog open={currentViewType === 'settings'} onOpenChange={(open) => !open && closeView()}>
        <DialogContent className="bg-gray-900/95 border border-purple-500/20 backdrop-blur-md max-w-[95vw] max-h-[90vh] overflow-y-auto custom-scrollbar">
          <DialogHeader className="border-b border-purple-500/10 pb-4">
            <DialogTitle className="text-xl text-white flex items-center gap-2">
              <Settings2 className="h-5 w-5 text-purple-400" />
              Session Settings
            </DialogTitle>
          </DialogHeader>
          <div className="pt-4">
            {currentSessionId && sessions.find(s => s.id === currentSessionId) && (
              <SessionSettings
                session={sessions.find(s => s.id === currentSessionId)!}
                onSettingsUpdated={() => {
                  closeView();
                  // Trigger a refresh of the sessions list without page reload
                  fetchSessionData();
                }}
              />
            )}
          </div>
          <div className="flex justify-end mt-6 pt-4 border-t border-purple-500/10">
            <Button
              variant="outline"
              onClick={closeView}
              className="bg-gray-800 hover:bg-gray-700 text-white border-gray-700"
            >
              <X className="h-4 w-4 mr-2" />
              Close Settings
            </Button>
          </div>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
